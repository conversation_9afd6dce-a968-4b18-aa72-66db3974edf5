# Analisi: Rifacimento Pagina Visualizzazione Viaggio Multi-Tappa

## User Story
**Come utente, vorrei poter visualizzare il viaggio in modalità multi-tappa in modo che possa comprendere meglio ogni singolo spostamento del mio viaggio.**

## Situazione Attuale

### Pagina Esistente
La pagina `/trips/[slug]/page.tsx` esiste già e supporta parzialmente i viaggi multi-tappa, ma non segue il design del mockup in `.claude/mockup`.

### Componenti Esistenti Riutilizzabili
1. **StageDisplay.tsx** - Componente per visualizzare singole tappe
2. **StageTimeline.tsx** - Timeline delle tappe (già in uso)
3. **GPXSectionStage.tsx** - Sezione GPX per le tappe
4. **MediaGallery.tsx** - Galleria media generale
5. **AccessGate.tsx** - Controllo accesso premium

### Mockup di Riferimento
Il mockup in `.claude/mockup` definisce:
- **TripChips.tsx** - Chip colorati per durata, location, terreno, stagioni
- **TripMeta.tsx** - Metadati autore e data pubblicazione  
- **TripStage.tsx** - Visualizzazione dettagliata delle singole tappe
- **ImageGallery.tsx** - Carousel delle immagini
- **GPXSection.tsx** - Sezione GPX con download e mappa

### Modelli Dati
- **Trip** con `stages[]` (relazione 1:N con Stage)
- **Stage** con `media`, `gpxFile`, metadati di percorso
- **MediaItem** per immagini e video
- **GpxFile** con metadati del percorso

## Gap Analysis

### Cosa Manca
1. **Stile Grafico**: La pagina attuale non rispetta il design del mockup
2. **Sezione Generale**: Manca la sezione info generali (titolo, chip, meta, descrizione, carousel)
3. **Carosello Immagini**: Deve aggregare le immagini di tutte le tappe
4. **Layout Tappe**: Le tappe devono essere visualizzate come nel mockup TripStage
5. **Componenti UI**: Mancano i componenti chip, meta, e il nuovo layout

### Cosa Funziona
1. **Fetch dei Dati**: I dati di trip e stages vengono recuperati correttamente
2. **Permessi**: La gestione dei permessi di accesso funziona
3. **Componente Stage**: StageDisplay funziona ma va allineato al design
4. **GPX Handling**: La gestione GPX è già implementata

## Requisiti Implementazione

### Criteri di Accettazione Mappati
1. **Sezione Generale Completa**
   - Titolo del viaggio
   - Chip colorati (durata, location, terreno, stagioni)
   - Meta info (autore, data creazione)
   - Descrizione generale
   - Carosello immagini (aggregate da tutte le tappe)
   - Tags e caratteristiche

2. **Sezione Tappe del Viaggio**
   - Ogni tappa con: titolo, descrizione testuale (NON markdown), foto, sezione GPX
   - Link download GPX + mappa per ogni tappa
   - Design secondo mockup TripStage

### Vincoli Tecnici
- La descrizione delle tappe è testo semplice (non markdown)
- Il carosello aggrega le immagini delle tappe
- Rispettare il design grafico del mockup
- Mantenere i controlli di accesso esistenti

## Strategia di Implementazione

### Opzione 1: Rifacimento Completo (RACCOMANDATO)
**Pro:**
- Allineamento totale al mockup
- Codice più pulito e mantenibile
- Migliore user experience
- Facilità di test

**Contro:**  
- Più tempo di sviluppo
- Rischio di regressioni temporanee

### Opzione 2: Refactoring Incrementale
**Pro:**
- Meno rischi di breaking changes
- Sviluppo più veloce

**Contro:**
- Codice ibrido e meno pulito
- Difficile mantenere coerenza design
- Più difficile da testare

## Componenti da Creare/Aggiornare

### Nuovi Componenti (dal mockup)
1. **TripChips** - Chip colorati per metadati
2. **TripMeta** - Autore e data pubblicazione  
3. **TripInfo** - Sezione informazioni generali
4. **ImageGallery** - Carosello immagini con thumbnail

### Componenti da Aggiornare
1. **StageDisplay** - Allineare design al mockup TripStage
2. **GPXSectionStage** - Verificare allineamento con GPXSection del mockup

### Pagina da Rifactorizzare
- **trips/[slug]/page.tsx** - Layout completo secondo mockup

## Stima Effort
- **Creazione componenti UI**: 2-3h  
- **Rifacimento pagina**: 2-3h
- **Aggiornamento componenti esistenti**: 1-2h
- **Testing e debug**: 1-2h
- **TOTALE**: 6-10h

## Rischi e Mitigazioni
1. **Breaking changes** → Test completo prima di commit
2. **Performance carosello** → Lazy loading immagini
3. **Accesso GPX** → Mantenere logica AccessGate esistente
4. **Responsive design** → Test su mobile/tablet